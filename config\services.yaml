# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
    my_workflow_registry:
        alias: 'workflow.registry'
        public: true

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    App\Service\LdapService:
        arguments:
            $host: '%env(LDAP_HOST)%'
            $username: '%env(LDAP_USERNAME)%'
            $password: '%env(LDAP_PASSWORD)%'

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
    App\Command\MigrateLegacyDataOptimizedCommand:
        arguments:
            $oldDb: '@doctrine.dbal.legacy_connection'

    App\Command\MigrateDMOCommand:
        arguments:
            $oldDb: '@doctrine.dbal.legacy_dmo_connection'

    App\Command\MigrateMaterialsCommand:
        arguments:
            $scmDb: '@doctrine.dbal.legacy_scm_connection'

    # Extension Twig pour les types de matériaux
    App\Twig\MaterialTypeExtension:
        tags: ['twig.extension']

    # Service de réinitialisation de doctype
    App\Service\DoctypeResetService:
        arguments:
            $entityManager: '@doctrine.orm.entity_manager'
            $logger: '@logger'
            $workflowRegistry: '@workflow.registry'
